server:
  port: 3978

spring:
  application:
    name: teams-webhook
  main:
    allow-bean-definition-overriding: true

# Bot Framework configuration
bot:
  app-id: c82b1652-2075-4440-bf01-d348175f383e
  app-password: ****************************************
  tenant-id: 759d6a50-5dd1-4627-a460-636fb0dc48e3

# Logging configuration
logging:
  level:
    com.example.teamswebhook: INFO
    com.microsoft.bot: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/teams-bot.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
