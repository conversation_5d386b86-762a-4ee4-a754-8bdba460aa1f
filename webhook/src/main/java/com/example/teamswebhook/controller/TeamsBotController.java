package com.example.teamswebhook.controller;

import com.example.teamswebhook.service.MessageProcessorService;
import com.microsoft.bot.schema.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

@RestController
public class TeamsBotController {

    private static final Logger logger = LoggerFactory.getLogger(TeamsBotController.class);

    @Autowired
    private MessageProcessorService messageProcessorService;

    @PostMapping("/api/messages")
    public CompletableFuture<ResponseEntity<Object>> messages(@RequestBody Activity activity,
                                                            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            // Xử lý message và gửi phản hồi
            if ("message".equals(activity.getType())) {
                return messageProcessorService.processMessage(activity)
                        .thenApply(result -> ResponseEntity.ok().build())
                        .exceptionally(ex -> {
                            logger.error("Error processing message", ex);
                            return ResponseEntity.status(500).build();
                        });
            } else {
                // Xử lý các loại activity khác (conversationUpdate, installationUpdate, etc.)
                return messageProcessorService.processOtherActivity(activity)
                        .thenApply(result -> ResponseEntity.ok().build())
                        .exceptionally(ex -> {
                            logger.error("Error processing activity", ex);
                            return ResponseEntity.status(500).build();
                        });
            }

        } catch (Exception e) {
            logger.error("Error in webhook endpoint", e);
            return CompletableFuture.completedFuture(ResponseEntity.status(500).build());
        }
    }
}
