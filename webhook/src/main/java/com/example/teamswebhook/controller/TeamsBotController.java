package com.example.teamswebhook.controller;

import com.example.teamswebhook.service.AuthenticationService;
import com.example.teamswebhook.service.MessageProcessorService;
import com.microsoft.bot.schema.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

@RestController
public class TeamsBotController {

    private static final Logger logger = LoggerFactory.getLogger(TeamsBotController.class);

    @Autowired
    private MessageProcessorService messageProcessorService;

    @Autowired
    private AuthenticationService authenticationService;

    @PostMapping("/api/messages")
    public CompletableFuture<ResponseEntity<Object>> messages(@RequestBody Activity activity,
                                                            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("Received activity: {}", activity);

        // Log authentication info để debug
        authenticationService.logAuthenticationInfo(authHeader, activity);
        authenticationService.authenticateRequest(authHeader, activity);
                                                                
        try {
            // Xác thực request trước khi xử lý
            boolean isAuthenticated = authenticationService.authenticateSimple(authHeader, activity);

            if (!isAuthenticated) {
                logger.warn("❌ Authentication failed for activity: {}", activity.getId());
                return CompletableFuture.completedFuture(ResponseEntity.status(401).build());
            }

            logger.info("✅ Authentication successful, processing activity: {}", activity.getType());
            // Xử lý message và gửi phản hồi
            if ("message".equals(activity.getType())) {
                return messageProcessorService.processMessage(activity)
                        .thenApply(result -> ResponseEntity.ok().build())
                        .exceptionally(ex -> {
                            logger.error("Error processing message", ex);
                            return ResponseEntity.status(500).build();
                        });
            } else {
                // Xử lý các loại activity khác (conversationUpdate, installationUpdate, etc.)
                return messageProcessorService.processOtherActivity(activity)
                        .thenApply(result -> ResponseEntity.ok().build())
                        .exceptionally(ex -> {
                            logger.error("Error processing activity", ex);
                            return ResponseEntity.status(500).build();
                        });
            }

        } catch (Exception e) {
            logger.error("Error in webhook endpoint", e);
            return CompletableFuture.completedFuture(ResponseEntity.status(500).build());
        }
    }

    /**
     * Alternative endpoint với JWT authentication nâng cao
     */
    @PostMapping("/api/messages/secure")
    public CompletableFuture<ResponseEntity<Object>> messagesSecure(@RequestBody Activity activity,
                                                                   @RequestHeader(value = "Authorization", required = true) String authHeader) {

        logger.info("Received secure activity: {}", activity);
        authenticationService.logAuthenticationInfo(authHeader, activity);

        // Sử dụng JWT authentication nâng cao
        return authenticationService.authenticateRequest(authHeader, activity)
            .thenCompose(isAuthenticated -> {
                if (!isAuthenticated) {
                    logger.warn("❌ JWT Authentication failed for activity: {}", activity.getId());
                    return CompletableFuture.completedFuture(ResponseEntity.status(401).build());
                }

                logger.info("✅ JWT Authentication successful, processing activity: {}", activity.getType());

                // Xử lý message sau khi xác thực thành công
                if ("message".equals(activity.getType())) {
                    return messageProcessorService.processMessage(activity)
                            .thenApply(result -> ResponseEntity.ok().build())
                            .exceptionally(ex -> {
                                logger.error("Error processing secure message", ex);
                                return ResponseEntity.status(500).build();
                            });
                } else {
                    return messageProcessorService.processOtherActivity(activity)
                            .thenApply(result -> ResponseEntity.ok().build())
                            .exceptionally(ex -> {
                                logger.error("Error processing secure activity", ex);
                                return ResponseEntity.status(500).build();
                            });
                }
            })
            .exceptionally(ex -> {
                logger.error("Error in secure webhook endpoint", ex);
                return ResponseEntity.status(500).build();
            });
    }
}
