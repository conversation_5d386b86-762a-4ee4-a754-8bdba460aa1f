package com.example.teamswebhook.service;

import com.microsoft.bot.connector.authentication.JwtTokenValidation;
import com.microsoft.bot.connector.authentication.SimpleCredentialProvider;
import com.microsoft.bot.schema.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

@Service
public class AuthenticationService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);
    
    @Value("${bot.app-id:}")
    private String appId;
    
    @Value("${bot.app-password:}")
    private String appPassword;
    
    @Value("${bot.tenant-id:}")
    private String tenantId;
    
    /**
     * Xác thực JWT token từ Bot Framework
     * @param authHeader Authorization header từ request
     * @param activity Activity object từ Bot Framework
     * @return CompletableFuture<Boolean> - true nếu xác thực thành công
     */
    public CompletableFuture<Boolean> authenticateRequest(String authHeader, Activity activity) {
        try {
            logger.info("=== AUTHENTICATION START ===");
            logger.info("Auth Header: {}", authHeader != null ? "Bearer ***" : "null");
            logger.info("Service URL: {}", activity.getServiceUrl());
            logger.info("Channel ID: {}", activity.getChannelId());
            
            // Kiểm tra auth header format
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                logger.warn("Invalid or missing Authorization header");
                return CompletableFuture.completedFuture(false);
            }
            
            // Extract JWT token
            String token = authHeader.substring(7); // Remove "Bearer " prefix
            logger.debug("JWT Token length: {}", token.length());
            
            // Tạo credential provider
            SimpleCredentialProvider credentialProvider = new SimpleCredentialProvider(appId, appPassword);
            
            // Validate JWT token với Bot Framework
            return JwtTokenValidation.authenticateRequest(
                activity,
                authHeader,
                credentialProvider,
                null, // channel service (null for default)
                null  // authentication configuration (null for default)
            ).thenApply(claimsIdentity -> {
                if (claimsIdentity != null && claimsIdentity.isAuthenticated()) {
                    logger.info("✅ JWT Authentication successful");
                    logger.info("Claims Identity authenticated: {}", claimsIdentity.isAuthenticated());
                    logger.info("Claims count: {}", claimsIdentity.claims().size());
                    return true;
                } else {
                    logger.warn("❌ JWT Authentication failed - Invalid claims identity");
                    return false;
                }
            }).exceptionally(ex -> {
                logger.error("❌ Authentication failed with exception", ex);
                return false;
            });
            
        } catch (Exception e) {
            logger.error("❌ Authentication error", e);
            return CompletableFuture.completedFuture(false);
        }
    }
    
    /**
     * Xác thực đơn giản bằng cách kiểm tra format và source
     * @param authHeader Authorization header
     * @param activity Activity object
     * @return boolean - true nếu xác thực thành công
     */
    public boolean authenticateSimple(String authHeader, Activity activity) {
        try {
            logger.info("=== SIMPLE AUTHENTICATION ===");
            
            // Kiểm tra auth header
            if (authHeader == null || authHeader.trim().isEmpty()) {
                logger.warn("❌ Missing Authorization header");
                return false;
            }
            
            if (!authHeader.startsWith("Bearer ")) {
                logger.warn("❌ Invalid Authorization header format");
                return false;
            }
            
            // Kiểm tra service URL từ Bot Framework
            String serviceUrl = activity.getServiceUrl();
            if (serviceUrl == null || 
                (!serviceUrl.contains("botframework.com") && 
                 !serviceUrl.contains("smba.trafficmanager.net"))) {
                logger.warn("❌ Invalid service URL: {}", serviceUrl);
                return false;
            }
            
            // Kiểm tra channel ID hợp lệ
            String channelId = activity.getChannelId();
            if (channelId == null || 
                (!channelId.equals("msteams") && 
                 !channelId.equals("webchat") && 
                 !channelId.equals("directline"))) {
                logger.warn("❌ Invalid channel ID: {}", channelId);
                return false;
            }
            
            logger.info("✅ Simple authentication successful");
            logger.info("Service URL: {}", serviceUrl);
            logger.info("Channel ID: {}", channelId);
            
            return true;
            
        } catch (Exception e) {
            logger.error("❌ Simple authentication error", e);
            return false;
        }
    }
    
    /**
     * Log thông tin authentication để debug
     */
    public void logAuthenticationInfo(String authHeader, Activity activity) {
        System.out.println("=== AUTHENTICATION INFO ===");
        System.out.println("Auth Header: " + (authHeader != null ? "Bearer ***" : "null"));
        System.out.println("Service URL: " + activity.getServiceUrl());
        System.out.println("Channel ID: " + activity.getChannelId());
        System.out.println("Activity ID: " + activity.getId());
        System.out.println("From: " + (activity.getFrom() != null ? activity.getFrom().getName() : "null"));
        System.out.println("Bot App ID: " + appId);
        System.out.println("============================");
    }
}
