package com.example.teamswebhook.service;

import com.microsoft.bot.schema.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

@Service
public class MessageProcessorService {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageProcessorService.class);
    
    @Autowired
    private BotResponseService botResponseService;
    
    public CompletableFuture<Void> processMessage(Activity activity) {
        try {
            String messageText = activity.getText();
            String userName = activity.getFrom() != null ? activity.getFrom().getName() : "Unknown";
            
            // In thông tin message ra console
            System.out.println("=== TEAMS MESSAGE RECEIVED ===");
            System.out.println("Activity Type: " + activity.getType());
            System.out.println("Channel ID: " + activity.getChannelId());
            System.out.println("Timestamp: " + LocalDateTime.now());
            
            if (activity.getFrom() != null) {
                System.out.println("From: " + activity.getFrom().getName() + " (ID: " + activity.getFrom().getId() + ")");
            }
            
            if (messageText != null && !messageText.isEmpty()) {
                System.out.println("Message: " + messageText);
            }
            
            if (activity.getConversation() != null) {
                System.out.println("Conversation ID: " + activity.getConversation().getId());
            }
            
            System.out.println("===============================");
            
            // Log vào file
            logger.info("Teams activity received - Type: {}, From: {}, Text: {}", 
                       activity.getType(), userName, messageText);
            
            // Xử lý message và tạo phản hồi
            String replyText = generateReply(messageText, userName);
            
            // Gửi phản hồi về Teams
            return botResponseService.sendReply(activity, replyText)
                    .thenApply(response -> null);
            
        } catch (Exception e) {
            logger.error("Error processing message", e);
            System.err.println("Error processing message: " + e.getMessage());
            return CompletableFuture.completedFuture(null);
        }
    }
    
    private String generateReply(String messageText, String userName) {
        if (messageText == null || messageText.trim().isEmpty()) {
            return "Xin chào " + userName + "! Tôi đã nhận được tin nhắn của bạn.";
        }
        
        String lowerMessage = messageText.toLowerCase().trim();
        
        // Các phản hồi thông minh dựa trên nội dung tin nhắn
        if (lowerMessage.contains("hello") || lowerMessage.contains("hi") || lowerMessage.contains("xin chào")) {
            return "Xin chào " + userName + "! Rất vui được gặp bạn! 👋";
        }
        
        if (lowerMessage.contains("how are you") || lowerMessage.contains("bạn khỏe không")) {
            return "Tôi khỏe, cảm ơn bạn đã hỏi! Bạn thì sao? 😊";
        }
        
        if (lowerMessage.contains("thank") || lowerMessage.contains("cảm ơn")) {
            return "Không có gì, " + userName + "! Tôi luôn sẵn sàng giúp đỡ bạn! 🤝";
        }
        
        if (lowerMessage.contains("help") || lowerMessage.contains("giúp")) {
            return "Tôi có thể giúp gì cho bạn? Hãy cho tôi biết bạn cần hỗ trợ gì nhé! 🆘";
        }
        
        if (lowerMessage.contains("time") || lowerMessage.contains("giờ")) {
            return "Hiện tại là: " + LocalDateTime.now().toString() + " ⏰";
        }
        
        if (lowerMessage.contains("weather") || lowerMessage.contains("thời tiết")) {
            return "Tôi chưa thể kiểm tra thời tiết, nhưng hy vọng hôm nay là một ngày đẹp trời! ☀️";
        }
        
        if (lowerMessage.contains("bye") || lowerMessage.contains("goodbye") || lowerMessage.contains("tạm biệt")) {
            return "Tạm biệt " + userName + "! Hẹn gặp lại bạn sớm! 👋";
        }
        
        // Phản hồi mặc định
        return "Cảm ơn bạn đã gửi tin nhắn: \"" + messageText + "\". Tôi đã ghi nhận và sẽ xử lý sớm nhất có thể! ✅";
    }
    
    public CompletableFuture<Void> processOtherActivity(Activity activity) {
        try {
            String activityType = activity.getType();
            String userName = activity.getFrom() != null ? activity.getFrom().getName() : "Unknown";
            
            System.out.println("=== TEAMS ACTIVITY RECEIVED ===");
            System.out.println("Activity Type: " + activityType);
            System.out.println("From: " + userName);
            System.out.println("Timestamp: " + LocalDateTime.now());
            System.out.println("===============================");
            
            logger.info("Teams activity received - Type: {}, From: {}", activityType, userName);
            
            // Xử lý các loại activity khác
            String responseText = null;
            
            switch (activityType) {
                case "conversationUpdate":
                    if (activity.getMembersAdded() != null && !activity.getMembersAdded().isEmpty()) {
                        responseText = "Chào mừng bạn đến với Teams Bot! Tôi sẵn sàng hỗ trợ bạn! 🎉";
                    }
                    break;
                    
                case "installationUpdate":
                    responseText = "Bot đã được cài đặt thành công! Hãy gửi tin nhắn để bắt đầu trò chuyện! 🚀";
                    break;
                    
                default:
                    logger.info("Received activity type: {} - no specific response", activityType);
                    break;
            }
            
            // Gửi phản hồi nếu có
            if (responseText != null) {
                return botResponseService.sendMessage(activity, responseText)
                        .thenApply(response -> null);
            }
            
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            logger.error("Error processing activity", e);
            System.err.println("Error processing activity: " + e.getMessage());
            return CompletableFuture.completedFuture(null);
        }
    }
}
