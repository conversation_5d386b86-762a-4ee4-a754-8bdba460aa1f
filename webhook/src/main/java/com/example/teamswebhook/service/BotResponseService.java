package com.example.teamswebhook.service;

import com.microsoft.bot.connector.authentication.MicrosoftAppCredentials;
import com.microsoft.bot.connector.rest.RestConnectorClient;
import com.microsoft.bot.schema.Activity;
import com.microsoft.bot.schema.ResourceResponse;
import com.nimbusds.oauth2.sdk.util.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
public class BotResponseService {
    
    private static final Logger logger = LoggerFactory.getLogger(BotResponseService.class);
    
    @Value("${bot.app-id:}")
    private String appId;
    
    @Value("${bot.app-password:}")
    private String appPassword;
    
    @Value("${bot.tenant-id:}")
    private String tenantId;
    
    public CompletableFuture<ResourceResponse> sendReply(Activity originalActivity, String replyText) {
        // 1) Gán override OAuth endpoint ngay trước khi tạo credentials
        if (StringUtils.isNotBlank(tenantId)) {
            String oauthEndpoint = String.format(
                "https://login.microsoftonline.com/%s/oauth2/v2.0/token",
                tenantId
            );
            System.setProperty(
                "MicrosoftAppCredentials_OAuthEndpoint",
                oauthEndpoint
            );
            logger.debug("Overriding OAuth endpoint to {}", oauthEndpoint);
        }

        // 2) Tạo activity reply
        Activity replyActivity = originalActivity.createReply(replyText);

        // 3) Nếu chưa có credentials → demo mode
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(appPassword)) {
            logger.info("DEMO MODE – Would send reply to {}: {}",
                        originalActivity.getFrom().getName(), replyText);
            return CompletableFuture.completedFuture(new ResourceResponse());
        }

        // 4) Tạo client và gửi
        try {
            MicrosoftAppCredentials credentials = new MicrosoftAppCredentials(appId, appPassword);
            RestConnectorClient connector =
                new RestConnectorClient(originalActivity.getServiceUrl(), credentials);


            return connector.getConversations()
                            .replyToActivity(
                                originalActivity.getConversation().getId(),
                                originalActivity.getId(),
                                replyActivity
                            )
                            .thenApply(response -> {
                                logger.info("Reply sent to {} – ResponseId: {}",
                                            originalActivity.getFrom().getName(),
                                            response.getId());
                                return response;
                            })
                            .exceptionally(ex -> {
                                logger.error("Failed to send reply to {}",
                                             originalActivity.getFrom().getName(), ex);
                                return new ResourceResponse();
                            });

        } catch (Exception e) {
            logger.error("Unexpected error in sendReply", e);
            return CompletableFuture.completedFuture(new ResourceResponse());
        }
    }
    
    public CompletableFuture<ResourceResponse> sendMessage(Activity originalActivity, String messageText) {
        try {
            // Tạo activity mới (không phải reply) - sử dụng createReply và clear replyToId
            Activity messageActivity = originalActivity.createReply(messageText);
            messageActivity.setReplyToId(null); // Clear reply ID để thành message mới

            // Nếu không có app credentials, chỉ log và return
            if (appId == null || appId.isEmpty() || appPassword == null || appPassword.isEmpty()) {
                logger.info("No bot credentials configured. Would send message: {}", messageText);
                System.out.println("=== WOULD SEND MESSAGE ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Message: " + messageText);
                System.out.println("==========================");
                return CompletableFuture.completedFuture(new ResourceResponse());
            }

            // Tạo connector client với credentials
            MicrosoftAppCredentials credentials = new MicrosoftAppCredentials(appId, appPassword);
            RestConnectorClient connector = new RestConnectorClient(originalActivity.getServiceUrl(), credentials);

            // Gửi message
            return connector.getConversations().sendToConversation(
                    originalActivity.getConversation().getId(),
                    messageActivity
            ).thenApply(response -> {
                logger.info("Message sent successfully to {}: {}", originalActivity.getFrom().getName(), messageText);
                System.out.println("=== MESSAGE SENT ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Message: " + messageText);
                System.out.println("Response ID: " + response.getId());
                System.out.println("====================");
                return response;
            }).exceptionally(ex -> {
                logger.error("Failed to send message", ex);
                System.err.println("Failed to send message: " + ex.getMessage());
                return new ResourceResponse();
            });

        } catch (Exception e) {
            logger.error("Error creating message", e);
            System.err.println("Error creating message: " + e.getMessage());
            return CompletableFuture.completedFuture(new ResourceResponse());
        }
    }
}
