package com.example.teamswebhook.service;

import com.microsoft.bot.connector.authentication.MicrosoftAppCredentials;
import com.microsoft.bot.connector.rest.RestConnectorClient;
import com.microsoft.bot.schema.Activity;
import com.microsoft.bot.schema.ResourceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
public class BotResponseService {
    
    private static final Logger logger = LoggerFactory.getLogger(BotResponseService.class);
    
    @Value("${bot.app-id:}")
    private String appId;
    
    @Value("${bot.app-password:}")
    private String appPassword;
    
    public CompletableFuture<ResourceResponse> sendReply(Activity originalActivity, String replyText) {
        try {
            // Tạo activity phản hồi sử dụng createReply
            Activity replyActivity = originalActivity.createReply(replyText);

            // Nếu không có app credentials, chỉ log và return
            if (appId == null || appId.isEmpty() || appPassword == null || appPassword.isEmpty()) {
                logger.info("No bot credentials configured. Would send reply: {}", replyText);
                System.out.println("=== WOULD SEND REPLY ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Reply: " + replyText);
                System.out.println("========================");
                return CompletableFuture.completedFuture(new ResourceResponse());
            }

            // Tạo connector client với credentials
            MicrosoftAppCredentials credentials = new MicrosoftAppCredentials(appId, appPassword);
            RestConnectorClient connector = new RestConnectorClient(originalActivity.getServiceUrl(), credentials);

            // Gửi phản hồi
            return connector.getConversations().replyToActivity(
                    originalActivity.getConversation().getId(),
                    originalActivity.getId(),
                    replyActivity
            ).thenApply(response -> {
                logger.info("Reply sent successfully to {}: {}", originalActivity.getFrom().getName(), replyText);
                System.out.println("=== REPLY SENT ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Reply: " + replyText);
                System.out.println("Response ID: " + response.getId());
                System.out.println("==================");
                return response;
            }).exceptionally(ex -> {
                logger.error("Failed to send reply", ex);
                System.err.println("Failed to send reply: " + ex.getMessage());
                return new ResourceResponse();
            });

        } catch (Exception e) {
            logger.error("Error creating reply", e);
            System.err.println("Error creating reply: " + e.getMessage());
            return CompletableFuture.completedFuture(new ResourceResponse());
        }
    }
    
    public CompletableFuture<ResourceResponse> sendMessage(Activity originalActivity, String messageText) {
        try {
            // Tạo activity mới (không phải reply) - sử dụng createReply và clear replyToId
            Activity messageActivity = originalActivity.createReply(messageText);
            messageActivity.setReplyToId(null); // Clear reply ID để thành message mới

            // Nếu không có app credentials, chỉ log và return
            if (appId == null || appId.isEmpty() || appPassword == null || appPassword.isEmpty()) {
                logger.info("No bot credentials configured. Would send message: {}", messageText);
                System.out.println("=== WOULD SEND MESSAGE ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Message: " + messageText);
                System.out.println("==========================");
                return CompletableFuture.completedFuture(new ResourceResponse());
            }

            // Tạo connector client với credentials
            MicrosoftAppCredentials credentials = new MicrosoftAppCredentials(appId, appPassword);
            RestConnectorClient connector = new RestConnectorClient(originalActivity.getServiceUrl(), credentials);

            // Gửi message
            return connector.getConversations().sendToConversation(
                    originalActivity.getConversation().getId(),
                    messageActivity
            ).thenApply(response -> {
                logger.info("Message sent successfully to {}: {}", originalActivity.getFrom().getName(), messageText);
                System.out.println("=== MESSAGE SENT ===");
                System.out.println("To: " + originalActivity.getFrom().getName());
                System.out.println("Message: " + messageText);
                System.out.println("Response ID: " + response.getId());
                System.out.println("====================");
                return response;
            }).exceptionally(ex -> {
                logger.error("Failed to send message", ex);
                System.err.println("Failed to send message: " + ex.getMessage());
                return new ResourceResponse();
            });

        } catch (Exception e) {
            logger.error("Error creating message", e);
            System.err.println("Error creating message: " + e.getMessage());
            return CompletableFuture.completedFuture(new ResourceResponse());
        }
    }
}
