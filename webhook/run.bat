@echo off
echo Starting Teams Bot Webhook with Response Feature...
echo.

REM Build the project
echo Building project...
call mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.

REM Start the application
echo Starting Spring Boot application on port 3978...
echo.
echo NEW FEATURES:
echo - <PERSON><PERSON> can now send replies back to Teams
echo - Smart responses based on message content
echo - Support for Vietnamese and English
echo.
echo Endpoints:
echo - Home: http://localhost:3978/
echo - Status: http://localhost:3978/status
echo - Webhook: http://localhost:3978/api/messages
echo - Health: http://localhost:3978/actuator/health
echo.
echo To use with ngrok, run in another terminal:
echo ngrok http 3978
echo.
echo To configure bot credentials, set environment variables:
echo set BOT_APP_ID=your-app-id
echo set BOT_APP_PASSWORD=your-app-password
echo.

call mvn spring-boot:run
