@echo off
echo Testing Teams Bot Response Feature...
echo.
echo Make sure the bot is running on http://localhost:3978
echo.

echo Testing Hello message...
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:3978/api/messages' -Method POST -ContentType 'application/json' -Body (Get-Content 'test-hello.json' -Raw)"
echo.

timeout /t 2 /nobreak >nul

echo Testing Help request...
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:3978/api/messages' -Method POST -ContentType 'application/json' -Body (Get-Content 'test-help.json' -Raw)"
echo.

timeout /t 2 /nobreak >nul

echo Testing original message...
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:3978/api/messages' -Method POST -ContentType 'application/json' -Body (Get-Content 'test-message.json' -Raw)"
echo.

echo Check the bot console to see the responses!
pause
