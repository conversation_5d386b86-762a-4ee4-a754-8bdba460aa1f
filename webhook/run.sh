#!/bin/bash

echo "Starting Teams Bot Webhook..."
echo

# Build the project
echo "Building project..."
mvn clean install -q

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"
echo

# Start the application
echo "Starting Spring Boot application on port 3978..."
echo
echo "Endpoints:"
echo "- Home: http://localhost:3978/"
echo "- Status: http://localhost:3978/status"
echo "- Webhook: http://localhost:3978/api/messages"
echo "- Health: http://localhost:3978/actuator/health"
echo
echo "To use with ngrok, run in another terminal:"
echo "ngrok http 3978"
echo

mvn spring-boot:run
