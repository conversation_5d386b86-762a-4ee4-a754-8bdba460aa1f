# Teams Bot Webhook với Response Feature

Dự án Spring Boot 3.5.3 để nhận message từ Azure Bot Framework (Teams Bot), in ra màn hình và **gửi phản hồi thông minh về Teams**.

## 🚀 Tính năng chính

- ✅ **Nhận webhook từ Teams Bot**
- ✅ **In message ra console** với thông tin chi tiết
- ✅ **Gửi phản hồi thông minh** về Teams Bot
- ✅ **Hỗ trợ đa ngôn ngữ** (Tiếng Việt & English)
- ✅ **Phản hồi thông minh** dựa trên nội dung tin nhắn
- ✅ **Log message** vào file
- ✅ **Health check endpoints**
- ✅ **Hỗ trợ ngrok** để public URL

## 📁 Cấu trúc dự án

```
src/
├── main/
│   ├── java/com/example/teamswebhook/
│   │   ├── TeamsWebhookApplication.java        # Main application
│   │   ├── config/
│   │   │   └── BotConfig.java                  # Bot configuration
│   │   ├── controller/
│   │   │   ├── TeamsBotController.java         # Bot webhook endpoint
│   │   │   └── HealthController.java           # Health check endpoints
│   │   └── service/
│   │       ├── BotResponseService.java         # Service gửi phản hồi về Teams
│   │       └── MessageProcessorService.java    # Service xử lý message & tạo phản hồi
│   └── resources/
│       └── application.yml                     # Configuration
```

## Cài đặt và chạy

### 1. Build project
```bash
mvn clean install
```

### 2. Chạy ứng dụng
```bash
mvn spring-boot:run
```

Hoặc:
```bash
java -jar target/teams-webhook-0.0.1-SNAPSHOT.jar
```

### 3. Sử dụng ngrok để public URL
```bash
# Cài đặt ngrok (nếu chưa có)
# Download từ https://ngrok.com/

# Public port 3978
ngrok http 3978
```

## Endpoints

- `GET /` - Trang chủ với thông tin service
- `GET /status` - Status check
- `POST /api/messages` - Webhook endpoint cho Teams Bot
- `GET /actuator/health` - Health check

## 🔧 Cấu hình Bot để gửi phản hồi

### Chế độ Demo (không cần credentials)
Bot sẽ chạy ở chế độ demo và chỉ in phản hồi ra console thay vì gửi thực sự về Teams.

### Chế độ Production (cần credentials)
Để bot có thể gửi phản hồi thực sự về Teams, cần cấu hình:

1. **Environment variables:**
   ```bash
   set BOT_APP_ID=your-bot-app-id
   set BOT_APP_PASSWORD=your-bot-app-password
   ```

2. **Hoặc trong application.yml:**
   ```yaml
   bot:
     app-id: your-bot-app-id
     app-password: your-bot-app-password
   ```

### Lấy Bot Credentials từ Azure
1. Vào Azure Portal → Bot Services
2. Chọn bot của bạn → Configuration
3. Copy **Microsoft App ID** và **Microsoft App Password**

## Logs

- Console: Hiển thị message real-time
- File: `logs/teams-bot.log`

## 📊 Ví dụ output khi nhận message và gửi phản hồi

### Input từ Teams:
```
=== TEAMS MESSAGE RECEIVED ===
Activity Type: message
Channel ID: msteams
Timestamp: 2025-07-22T16:14:53
From: John Doe (ID: 29:1234567890abcdef)
Message: Xin chào bot!
Conversation ID: 19:meeting_abc123def456@thread.v2
===============================
```

### Output phản hồi:
```
=== WOULD SEND REPLY ===
To: John Doe
Reply: Xin chào John Doe! Rất vui được gặp bạn! 👋
========================
```

## 🤖 Các loại phản hồi thông minh

Bot sẽ phản hồi khác nhau dựa trên nội dung tin nhắn:

- **Chào hỏi**: "hello", "hi", "xin chào" → Chào lại với emoji
- **Hỏi thăm**: "how are you", "bạn khỏe không" → Phản hồi lịch sự
- **Cảm ơn**: "thank", "cảm ơn" → Đáp lại cảm ơn
- **Yêu cầu giúp đỡ**: "help", "giúp" → Hướng dẫn hỗ trợ
- **Hỏi giờ**: "time", "giờ" → Hiển thị thời gian hiện tại
- **Thời tiết**: "weather", "thời tiết" → Phản hồi về thời tiết
- **Tạm biệt**: "bye", "goodbye", "tạm biệt" → Chào tạm biệt
- **Tin nhắn khác**: Xác nhận đã nhận và sẽ xử lý

## Testing

Sau khi setup ngrok và cấu hình bot, bạn có thể test bằng cách:

1. Gửi message trong Teams
2. Xem output trong console
3. Kiểm tra log file
4. Truy cập health endpoints

## Dependencies chính

- Spring Boot 3.5.3
- Microsoft Bot Framework SDK 4.14.1
- Jackson for JSON processing
- Spring Boot Actuator for monitoring
